#ifndef VECTOR_DOT_PRODUCT_HPP
#define VECTOR_DOT_PRODUCT_HPP

/**
 * @file vector_dot_product.hpp
 * @brief High-Performance Vector Dot Product System for Mixed-Precision Computing
 *
 * OVERVIEW
 * ========
 * This header defines a high-performance vector dot product system designed for
 * mixed-precision vector processing in machine learning accelerators. The system
 * supports both integer types (INT4, INT8, INT16) and floating-point types
 * (FP16, BF16, FP8E4M3, FP8E5M2) with optimized accumulation strategies.
 *
 * SUPPORTED DATA TYPES
 * ===================
 * 
 * Integer Types (No Pre-alignment Required):
 * - INT4: 4-bit signed integers with sign extension
 * - INT8: 8-bit signed integers
 * - INT16: 16-bit signed integers
 *
 * Floating-Point Types (Require Pre-alignment):
 * - FP16: IEEE 754 half-precision (1+5+10 bits)
 * - BF16: Brain Float 16 (1+8+7 bits)
 * - FP8E4M3: 4-bit exponent, 3-bit mantissa (1+4+3 bits)
 * - FP8E5M2: 5-bit exponent, 2-bit mantissa (1+5+2 bits)
 *
 * ALGORITHM OVERVIEW
 * ==================
 * The vector dot product algorithm performs element-wise multiplication of two
 * 32-element vectors and accumulates the results into a single FP32 output:
 *
 * result = Σ(a[i] * b[i]) for i = 0 to 31
 *
 * Key Features:
 * 1. **Mixed-Precision Support**: Handles all combinations of supported data types
 * 2. **FP32 Accumulation**: Uses robust FP32 accumulation to prevent overflow
 * 3. **Pre-alignment Integration**: Seamlessly integrates with fp_prealign system
 * 4. **Template Optimization**: Compile-time optimization through template specialization
 * 5. **C++11 Compatibility**: Fully compatible with C++11 standard
 *
 * PROCESSING WORKFLOW
 * ===================
 * 1. **Type Detection**: Determine if inputs are integer or floating-point types
 * 2. **Data Preparation**: 
 *    - Integer types: Direct processing with sign extension
 *    - Floating-point types: Use pre-aligned data from fp_prealign system
 * 3. **Element-wise Multiplication**: Multiply corresponding vector elements
 * 4. **Accumulation**: Accumulate products using FP32 precision
 * 5. **Result Generation**: Generate IEEE 754 compliant FP32 result
 *
 * ACCUMULATION STRATEGY
 * =====================
 * The system uses a sophisticated accumulation strategy to maintain precision:
 * - Integer operations use 64-bit intermediate accumulation
 * - Floating-point operations use FP32 accumulation with overflow detection
 * - Mixed-precision operations use appropriate type promotion
 * - Final result is always IEEE 754 compliant FP32
 *
 * INTEGRATION WITH PRE-ALIGNMENT
 * ===============================
 * For floating-point inputs, the system integrates with the existing fp_prealign
 * system to process pre-aligned mantissa data and public exponents:
 * - Input: 33-element arrays (32 aligned mantissas + 1 public exponent)
 * - Processing: Element-wise multiplication with exponent handling
 * - Output: Single FP32 result with proper IEEE 754 formatting
 *
 */

#include <cstdint>
#include <type_traits>
#include "fp_prealign.hpp"
#include "dcim_com.h"
namespace vector_dot_product {

// Processing constants
static constexpr size_t VECTOR_SIZE = 32;
static constexpr size_t PREALIGNED_SIZE = 33;  // 32 mantissas + 1 public exponent

/**
 * @brief Data type classification for processing strategy selection
 */
enum class DataTypeClass {
    INTEGER,        ///< Integer types (INT4, INT8, INT16)
    FLOATING_POINT  ///< Floating-point types (FP16, BF16, FP8E4, FP8E5)
};

/**
 * @brief Type traits for data type classification and characteristics
 * 
 * Provides compile-time information about data types to enable template
 * specialization and optimization.
 */
template<uint8_t DataType>
struct DataTypeTraits {
    static constexpr DataTypeClass type_class = DataTypeClass::INTEGER;
    static constexpr bool requires_prealignment = false;
    static constexpr uint8_t bit_width = 8;
    static constexpr const char* name = "UNKNOWN";
};

// Template specializations for supported data types
template<> struct DataTypeTraits<INT4> {
    static constexpr DataTypeClass type_class = DataTypeClass::INTEGER;
    static constexpr bool requires_prealignment = false;
    static constexpr uint8_t bit_width = 4;
    static constexpr const char* name = "INT4";
};

template<> struct DataTypeTraits<INT8> {
    static constexpr DataTypeClass type_class = DataTypeClass::INTEGER;
    static constexpr bool requires_prealignment = false;
    static constexpr uint8_t bit_width = 8;
    static constexpr const char* name = "INT8";
};

template<> struct DataTypeTraits<INT16> {
    static constexpr DataTypeClass type_class = DataTypeClass::INTEGER;
    static constexpr bool requires_prealignment = false;
    static constexpr uint8_t bit_width = 16;
    static constexpr const char* name = "INT16";
};

template<> struct DataTypeTraits<FP16> {
    static constexpr DataTypeClass type_class = DataTypeClass::FLOATING_POINT;
    static constexpr bool requires_prealignment = true;
    static constexpr uint8_t bit_width = 16;
    static constexpr const char* name = "FP16";
};

template<> struct DataTypeTraits<BF16> {
    static constexpr DataTypeClass type_class = DataTypeClass::FLOATING_POINT;
    static constexpr bool requires_prealignment = true;
    static constexpr uint8_t bit_width = 16;
    static constexpr const char* name = "BF16";
};

template<> struct DataTypeTraits<FP8E4> {
    static constexpr DataTypeClass type_class = DataTypeClass::FLOATING_POINT;
    static constexpr bool requires_prealignment = true;
    static constexpr uint8_t bit_width = 8;
    static constexpr const char* name = "FP8E4";
};

template<> struct DataTypeTraits<FP8E5> {
    static constexpr DataTypeClass type_class = DataTypeClass::FLOATING_POINT;
    static constexpr bool requires_prealignment = true;
    static constexpr uint8_t bit_width = 8;
    static constexpr const char* name = "FP8E5";
};

/**
 * @brief Abstract base class for accumulation strategies
 * 
 * Defines the interface for different accumulation strategies used in
 * vector dot product computation. Enables compile-time optimization
 * through template specialization.
 */
class AccumulationStrategyBase {
public:
    /**
     * @brief Virtual destructor for proper inheritance
     */
    virtual ~AccumulationStrategyBase() = default;
    
    /**
     * @brief Get the name of this accumulation strategy
     * @return Strategy name for debugging and logging
     */
    virtual const char* strategy_name() const = 0;
};

/**
 * @brief Integer accumulation strategy using 64-bit intermediate precision
 *
 * Provides robust accumulation for integer types with overflow protection
 * and proper scaling for FP32 output generation.
 */
class IntegerAccumulationStrategy : public AccumulationStrategyBase {
public:
    const char* strategy_name() const override { return "INTEGER_64BIT"; }

    /**
     * @brief Accumulate integer products with overflow protection
     * @param a First operand
     * @param b Second operand
     * @param accumulator Reference to 64-bit accumulator
     */
    static void accumulate_product(int32_t a, int32_t b, int64_t& accumulator) {
        accumulator += static_cast<int64_t>(a) * static_cast<int64_t>(b);
    }

    /**
     * @brief Convert 64-bit integer accumulator to FP32 result
     * @param accumulator 64-bit accumulated value
     * @return IEEE 754 compliant FP32 result
     */
    static uint32_t convert_to_fp32(int64_t accumulator);

    /**
     * @brief Sign-extend INT4 value to int32_t
     * @param value 4-bit value in lower bits of uint16_t
     * @return Sign-extended 32-bit integer
     */
    static int32_t sign_extend_int4(uint16_t value) {
        // Extract 4-bit value and sign-extend
        int32_t result = static_cast<int32_t>(value & 0xF);
        if (result & 0x8) {  // Check sign bit
            result |= 0xFFFFFFF0;  // Sign extend
        }
        return result;
    }

    /**
     * @brief Sign-extend INT8 value to int32_t
     * @param value 8-bit value in lower bits of uint16_t
     * @return Sign-extended 32-bit integer
     */
    static int32_t sign_extend_int8(uint16_t value) {
        return static_cast<int32_t>(static_cast<int8_t>(value & 0xFF));
    }

    /**
     * @brief Sign-extend INT16 value to int32_t
     * @param value 16-bit value
     * @return Sign-extended 32-bit integer
     */
    static int32_t sign_extend_int16(uint16_t value) {
        return static_cast<int32_t>(static_cast<int16_t>(value));
    }
};

/**
 * @brief Floating-point accumulation strategy using FP32 precision
 *
 * Provides accumulation for floating-point types with proper handling
 * of pre-aligned mantissa data and public exponents.
 */
class FloatingPointAccumulationStrategy : public AccumulationStrategyBase {
public:
    const char* strategy_name() const override { return "FLOATING_POINT_FP32"; }

    /**
     * @brief Accumulate floating-point products with overflow detection
     * @param a_mantissa Pre-aligned mantissa from first operand
     * @param b_mantissa Pre-aligned mantissa from second operand
     * @param accumulator Reference to FP32 accumulator
     */
    static void accumulate_product(uint16_t a_mantissa, uint16_t b_mantissa, float& accumulator);

    /**
     * @brief Generate FP32 result from accumulated value and public exponents
     * @param accumulator Accumulated FP32 value
     * @param a_public_exp Public exponent from first operand
     * @param b_public_exp Public exponent from second operand
     * @return IEEE 754 compliant FP32 result
     */
    static uint32_t generate_fp32_result(float accumulator, uint8_t a_public_exp, uint8_t b_public_exp);

    /**
     * @brief Convert pre-aligned mantissa to floating-point value
     * @param mantissa Pre-aligned mantissa value (signed, two's complement)
     * @return Floating-point representation of mantissa
     */
    static float mantissa_to_float(uint16_t mantissa) {
        // Handle two's complement signed mantissa
        int16_t signed_mantissa = static_cast<int16_t>(mantissa);
        return static_cast<float>(signed_mantissa);
    }

    /**
     * @brief Calculate scaling factor from public exponents
     * @param a_public_exp Public exponent from first operand
     * @param b_public_exp Public exponent from second operand
     * @return Scaling factor for final result
     */
    static float calculate_scaling_factor(uint8_t a_public_exp, uint8_t b_public_exp);
};

/**
 * @brief Main vector dot product computation engine
 * 
 * Template-based engine that provides optimized dot product computation
 * for all supported data type combinations.
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
class VectorDotProductEngine {
public:
    // Compile-time type information
    static constexpr bool a_is_integer = (DataTypeTraits<DataTypeA>::type_class == DataTypeClass::INTEGER);
    static constexpr bool b_is_integer = (DataTypeTraits<DataTypeB>::type_class == DataTypeClass::INTEGER);
    static constexpr bool both_integer = a_is_integer && b_is_integer;
    static constexpr bool both_floating = !a_is_integer && !b_is_integer;
    static constexpr bool mixed_precision = a_is_integer != b_is_integer;
    
    /**
     * @brief Compute vector dot product for the specified data type combination
     * @param a_data Input vector A (32 elements for integers, 33 for floating-point)
     * @param b_data Input vector B (32 elements for integers, 33 for floating-point)
     * @return FP32 dot product result as uint32_t bit representation
     */
    static uint32_t compute_dot_product(const uint16_t* a_data, const uint16_t* b_data);
    
private:
    /**
     * @brief Specialized computation for integer-integer combinations
     */
    static uint32_t compute_integer_integer(const uint16_t* a_data, const uint16_t* b_data);
    
    /**
     * @brief Specialized computation for floating-floating combinations
     */
    static uint32_t compute_floating_floating(const uint16_t* a_data, const uint16_t* b_data);
    
    /**
     * @brief Specialized computation for mixed-precision combinations
     */
    static uint32_t compute_mixed_precision(const uint16_t* a_data, const uint16_t* b_data);
};

/**
 * @brief Data type conversion utilities for mixed-precision operations
 */
class DataTypeConverter {
public:
    /**
     * @brief Convert integer data to standardized format for computation
     * @param data Raw integer data
     * @param data_type Integer data type (INT4, INT8, INT16)
     * @return Converted value as int32_t
     */
    static int32_t convert_integer_data(uint16_t data, uint8_t data_type) {
        switch (data_type) {
            case INT4:
                return IntegerAccumulationStrategy::sign_extend_int4(data);
            case INT8:
                return IntegerAccumulationStrategy::sign_extend_int8(data);
            case INT16:
                return IntegerAccumulationStrategy::sign_extend_int16(data);
            default:
                return 0;  // Error case
        }
    }

    /**
     * @brief Convert floating-point data to standardized format for computation
     * @param mantissa Pre-aligned mantissa data
     * @return Converted value as float
     */
    static float convert_floating_data(uint16_t mantissa) {
        return FloatingPointAccumulationStrategy::mantissa_to_float(mantissa);
    }

    /**
     * @brief Convert integer to floating-point for mixed-precision operations
     * @param integer_value Integer value
     * @param scaling_factor Scaling factor for proper magnitude
     * @return Floating-point representation
     */
    static float integer_to_float_scaled(int32_t integer_value, float scaling_factor) {
        return static_cast<float>(integer_value) * scaling_factor;
    }
};

/**
 * @brief Main template function for vector dot product computation
 * @tparam DataTypeA Data type of first vector
 * @tparam DataTypeB Data type of second vector
 * @param a_data Input vector A
 * @param b_data Input vector B
 * @return FP32 dot product result as uint32_t bit representation
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t compute_vector_dot_product(const uint16_t* a_data, const uint16_t* b_data);

} // namespace vector_dot_product

/**
 * @brief Integration utilities for seamless operation with fp_prealign system
 */
class PreAlignmentIntegration {
public:
    /**
     * @brief Check if data type requires pre-alignment processing
     * @param data_type Data type to check
     * @return true if pre-alignment is required
     */
    static bool requires_prealignment(uint8_t data_type) {
        return (data_type == FP16 || data_type == BF16 || data_type == BBF16 ||
                data_type == FP8E4 || data_type == FP8E5);
    }

    /**
     * @brief Perform pre-alignment for floating-point data if needed
     * @param input_data Raw input data (32 elements)
     * @param data_type Data type of input
     * @param output_data Pre-aligned output data (33 elements)
     * @return true if pre-alignment was performed successfully
     */
    static bool prealign_if_needed(const uint16_t* input_data, uint8_t data_type, uint16_t* output_data);

    /**
     * @brief Validate input data consistency for dot product operation
     * @param a_data Input vector A
     * @param a_data_type Data type of vector A
     * @param b_data Input vector B
     * @param b_data_type Data type of vector B
     * @return true if inputs are valid for dot product computation
     */
    static bool validate_inputs(const uint16_t* a_data, uint8_t a_data_type,
                               const uint16_t* b_data, uint8_t b_data_type);

    /**
     * @brief Get expected input size for given data type
     * @param data_type Data type to check
     * @return Expected number of elements (32 for integers, 33 for pre-aligned floating-point)
     */
    static size_t get_expected_input_size(uint8_t data_type) {
        return requires_prealignment(data_type) ? vector_dot_product::PREALIGNED_SIZE : vector_dot_product::VECTOR_SIZE;
    }
};

/**
 * @brief High-level interface for vector dot product with automatic pre-alignment
 */
class VectorDotProductInterface {
public:
    /**
     * @brief Compute dot product with automatic pre-alignment handling
     * @param a_raw Raw input vector A (32 elements)
     * @param a_data_type Data type of vector A
     * @param b_raw Raw input vector B (32 elements)
     * @param b_data_type Data type of vector B
     * @return FP32 dot product result as uint32_t bit representation
     */
    static uint32_t compute_with_auto_prealign(const uint16_t* a_raw, uint8_t a_data_type,
                                              const uint16_t* b_raw, uint8_t b_data_type);

    /**
     * @brief Compute dot product using pre-aligned data
     * @param a_prealigned Pre-aligned vector A (33 elements for FP, 32 for INT)
     * @param a_data_type Data type of vector A
     * @param b_prealigned Pre-aligned vector B (33 elements for FP, 32 for INT)
     * @param b_data_type Data type of vector B
     * @return FP32 dot product result as uint32_t bit representation
     */
    static uint32_t compute_with_prealigned(const uint16_t* a_prealigned, uint8_t a_data_type,
                                           const uint16_t* b_prealigned, uint8_t b_data_type);
};

// C-compatible function declarations
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief C wrapper for vector dot product computation
 * @param a_data Input vector A
 * @param a_data_type Data type of vector A
 * @param b_data Input vector B
 * @param b_data_type Data type of vector B
 * @return FP32 dot product result as uint32_t bit representation
 */
uint32_t vector_dot_product_c(const uint16_t* a_data, uint8_t a_data_type,
                              const uint16_t* b_data, uint8_t b_data_type);

/**
 * @brief C wrapper with automatic pre-alignment
 * @param a_raw Raw input vector A (32 elements)
 * @param a_data_type Data type of vector A
 * @param b_raw Raw input vector B (32 elements)
 * @param b_data_type Data type of vector B
 * @return FP32 dot product result as uint32_t bit representation
 */
uint32_t vector_dot_product_auto_c(const uint16_t* a_raw, uint8_t a_data_type,
                                   const uint16_t* b_raw, uint8_t b_data_type);

#ifdef __cplusplus
}
#endif

#endif // VECTOR_DOT_PRODUCT_HPP
