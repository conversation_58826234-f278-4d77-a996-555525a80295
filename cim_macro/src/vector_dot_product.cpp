/**
 * @file vector_dot_product.cpp
 * @brief High-Performance Vector Dot Product Implementation
 *
 * This implementation provides optimized vector dot product computation for
 * mixed-precision data types, integrating seamlessly with the existing
 * floating-point pre-alignment system.
 *
 * Key Features:
 * - Support for all integer types (INT4, INT8, INT16) and floating-point types
 * - Robust FP32 accumulation with overflow protection
 * - Template-based optimization for compile-time specialization
 * - Integration with fp_prealign system for floating-point data
 * - C++11 compatibility with comprehensive error handling
 *
 */

#include "../inc/vector_dot_product.hpp"
#include <cmath>
#include <cstring>
#include <algorithm>
using namespace fp_prealign;
namespace vector_dot_product {

// IEEE 754 FP32 bit manipulation utilities
union FP32Union {
    float f;
    uint32_t u;
    struct {
        uint32_t mantissa : 23;
        uint32_t exponent : 8;
        uint32_t sign : 1;
    } bits;
};

/**
 * @brief Convert 64-bit integer accumulator to IEEE 754 FP32
 */
uint32_t IntegerAccumulationStrategy::convert_to_fp32(int64_t accumulator) {
    if (accumulator == 0) {
        return 0;  // Positive zero
    }
    
    FP32Union result;
    result.f = static_cast<float>(accumulator);
    return result.u;
}

/**
 * @brief Accumulate floating-point products with proper scaling
 */
void FloatingPointAccumulationStrategy::accumulate_product(uint16_t a_mantissa, uint16_t b_mantissa, float& accumulator) {
    float a_float = mantissa_to_float(a_mantissa);
    float b_float = mantissa_to_float(b_mantissa);
    accumulator += a_float * b_float;
}

/**
 * @brief Calculate scaling factor from public exponents
 */
float FloatingPointAccumulationStrategy::calculate_scaling_factor(uint8_t a_public_exp, uint8_t b_public_exp) {
    // Combined exponent for the dot product result
    int32_t combined_exp = static_cast<int32_t>(a_public_exp) + static_cast<int32_t>(b_public_exp);
    
    // Convert to FP32 scaling factor
    // Note: This follows the same logic as dcim_macro.c for exponent handling
    return std::pow(2.0f, static_cast<float>(combined_exp));
}

/**
 * @brief Generate final FP32 result with proper exponent scaling
 */
uint32_t FloatingPointAccumulationStrategy::generate_fp32_result(float accumulator, uint8_t a_public_exp, uint8_t b_public_exp) {
    if (accumulator == 0.0f) {
        return 0;  // Positive zero
    }
    
    float scaling_factor = calculate_scaling_factor(a_public_exp, b_public_exp);
    float scaled_result = accumulator * scaling_factor;
    
    FP32Union result;
    result.f = scaled_result;
    return result.u;
}

/**
 * @brief Template specialization for integer-integer dot product
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_integer_integer(const uint16_t* a_data, const uint16_t* b_data) {
    static_assert(DataTypeTraits<DataTypeA>::type_class == DataTypeClass::INTEGER, "DataTypeA must be integer");
    static_assert(DataTypeTraits<DataTypeB>::type_class == DataTypeClass::INTEGER, "DataTypeB must be integer");
    
    int64_t accumulator = 0;
    
    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        int32_t a_val = DataTypeConverter::convert_integer_data(a_data[i], DataTypeA);
        int32_t b_val = DataTypeConverter::convert_integer_data(b_data[i], DataTypeB);
        
        IntegerAccumulationStrategy::accumulate_product(a_val, b_val, accumulator);
    }
    
    return IntegerAccumulationStrategy::convert_to_fp32(accumulator);
}

/**
 * @brief Template specialization for floating-floating dot product
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_floating_floating(const uint16_t* a_data, const uint16_t* b_data) {
    static_assert(DataTypeTraits<DataTypeA>::type_class == DataTypeClass::FLOATING_POINT, "DataTypeA must be floating-point");
    static_assert(DataTypeTraits<DataTypeB>::type_class == DataTypeClass::FLOATING_POINT, "DataTypeB must be floating-point");
    
    float accumulator = 0.0f;
    
    // Process 32 mantissa pairs
    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        FloatingPointAccumulationStrategy::accumulate_product(a_data[i], b_data[i], accumulator);
    }
    
    // Extract public exponents (stored at index 32)
    uint8_t a_public_exp = static_cast<uint8_t>(a_data[VECTOR_SIZE]);
    uint8_t b_public_exp = static_cast<uint8_t>(b_data[VECTOR_SIZE]);
    
    return FloatingPointAccumulationStrategy::generate_fp32_result(accumulator, a_public_exp, b_public_exp);
}

/**
 * @brief Template specialization for mixed-precision dot product
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_mixed_precision(const uint16_t* a_data, const uint16_t* b_data) {
    static_assert(DataTypeTraits<DataTypeA>::type_class != DataTypeTraits<DataTypeB>::type_class, "Types must be different classes");
    
    float accumulator = 0.0f;
    
    // Determine which is integer and which is floating-point
    const bool a_is_int = (DataTypeTraits<DataTypeA>::type_class == DataTypeClass::INTEGER);

    if (a_is_int) {
        // A is integer, B is floating-point
        uint8_t b_public_exp = static_cast<uint8_t>(b_data[VECTOR_SIZE]);
        float int_scaling = std::pow(2.0f, static_cast<float>(b_public_exp));
        
        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            int32_t a_val = DataTypeConverter::convert_integer_data(a_data[i], DataTypeA);
            float b_val = DataTypeConverter::convert_floating_data(b_data[i]);
            
            accumulator += DataTypeConverter::integer_to_float_scaled(a_val, int_scaling) * b_val;
        }
    } else {
        // A is floating-point, B is integer
        uint8_t a_public_exp = static_cast<uint8_t>(a_data[VECTOR_SIZE]);
        float int_scaling = std::pow(2.0f, static_cast<float>(a_public_exp));
        
        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            float a_val = DataTypeConverter::convert_floating_data(a_data[i]);
            int32_t b_val = DataTypeConverter::convert_integer_data(b_data[i], DataTypeB);
            
            accumulator += a_val * DataTypeConverter::integer_to_float_scaled(b_val, int_scaling);
        }
    }
    
    FP32Union result;
    result.f = accumulator;
    return result.u;
}

/**
 * @brief Main template function implementation
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_dot_product(const uint16_t* a_data, const uint16_t* b_data) {
    if (both_integer) {
        return compute_integer_integer(a_data, b_data);
    } else if (both_floating) {
        return compute_floating_floating(a_data, b_data);
    } else {
        return compute_mixed_precision(a_data, b_data);
    }
}

/**
 * @brief Main template function wrapper
 */
template<uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t compute_vector_dot_product(const uint16_t* a_data, const uint16_t* b_data) {
    return VectorDotProductEngine<DataTypeA, DataTypeB>::compute_dot_product(a_data, b_data);
}

// Explicit template instantiations for all supported combinations
// Integer-Integer combinations
template uint32_t compute_vector_dot_product<INT4, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT4, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT4, INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, INT16>(const uint16_t*, const uint16_t*);

// Floating-Floating combinations
template uint32_t compute_vector_dot_product<FP16, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP16, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP16, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP16, FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, FP8E5>(const uint16_t*, const uint16_t*);

// Mixed-precision combinations (Integer-Floating)
template uint32_t compute_vector_dot_product<INT4, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT4, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT4, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT4, FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT8, FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<INT16, FP8E5>(const uint16_t*, const uint16_t*);

// Mixed-precision combinations (Floating-Integer)
template uint32_t compute_vector_dot_product<FP16, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP16, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP16, INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<BF16, INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E4, INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<FP8E5, INT16>(const uint16_t*, const uint16_t*);

/**
 * @brief Implementation of pre-alignment integration utilities
 */
bool PreAlignmentIntegration::prealign_if_needed(const uint16_t* input_data, uint8_t data_type, uint16_t* output_data) {
    if (!input_data || !output_data) {
        return false;
    }

    if (!requires_prealignment(data_type)) {
        // For integer types, just copy the data
        std::memcpy(output_data, input_data, vector_dot_product::VECTOR_SIZE * sizeof(uint16_t));
        return true;
    }

    // Use the existing fp_prealign system for floating-point types
    switch (data_type) {
        case FP16:
            fp_prealign::float_prealign<fp_prealign::FP16>(input_data, output_data);
            return true;
        case BF16:
        case BBF16:
            fp_prealign::float_prealign<fp_prealign::BF16>(input_data, output_data);
            return true;
        case FP8E4:
            fp_prealign::float_prealign<fp_prealign::FP8E4>(input_data, output_data);
            return true;
        case FP8E5:
            fp_prealign::float_prealign<fp_prealign::FP8E5>(input_data, output_data);
            return true;
        default:
            return false;
    }
}

bool PreAlignmentIntegration::validate_inputs(const uint16_t* a_data, uint8_t a_data_type,
                                             const uint16_t* b_data, uint8_t b_data_type) {
    // Check for null pointers
    if (!a_data || !b_data) {
        return false;
    }

    // Check for valid data types
    bool a_valid = (a_data_type >= INT4 && a_data_type <= FP8E5);
    bool b_valid = (b_data_type >= INT4 && b_data_type <= FP8E5);

    return a_valid && b_valid;
}

/**
 * @brief Implementation of high-level vector dot product interface
 */
uint32_t VectorDotProductInterface::compute_with_auto_prealign(const uint16_t* a_raw, uint8_t a_data_type,
                                                              const uint16_t* b_raw, uint8_t b_data_type) {
    if (!PreAlignmentIntegration::validate_inputs(a_raw, a_data_type, b_raw, b_data_type)) {
        return 0;  // Invalid inputs
    }

    // Prepare data buffers
    uint16_t a_processed[vector_dot_product::PREALIGNED_SIZE];
    uint16_t b_processed[vector_dot_product::PREALIGNED_SIZE];

    // Pre-align data if needed
    if (!PreAlignmentIntegration::prealign_if_needed(a_raw, a_data_type, a_processed) ||
        !PreAlignmentIntegration::prealign_if_needed(b_raw, b_data_type, b_processed)) {
        return 0;  // Pre-alignment failed
    }

    // Compute dot product using pre-aligned data
    return compute_with_prealigned(a_processed, a_data_type, b_processed, b_data_type);
}

uint32_t VectorDotProductInterface::compute_with_prealigned(const uint16_t* a_prealigned, uint8_t a_data_type,
                                                           const uint16_t* b_prealigned, uint8_t b_data_type) {
    if (!PreAlignmentIntegration::validate_inputs(a_prealigned, a_data_type, b_prealigned, b_data_type)) {
        return 0;  // Invalid inputs
    }

    // Delegate to the C wrapper function which handles all type combinations
    return ::vector_dot_product_c(a_prealigned, a_data_type, b_prealigned, b_data_type);
}

} // namespace vector_dot_product

// C-compatible wrapper implementation
extern "C" {

/**
 * @brief C wrapper for vector dot product computation with comprehensive error handling
 */
uint32_t vector_dot_product_c(const uint16_t* a_data, uint8_t a_data_type,
                              const uint16_t* b_data, uint8_t b_data_type) {
    // Input validation
    if (!a_data || !b_data) {
        return 0;  // Return zero for null pointers
    }
    
    // Dispatch to appropriate template instantiation based on data types
    // This follows the same pattern as the fp_prealign system
    switch (a_data_type) {
        case INT4:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<INT4, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<INT4, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<INT4, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<INT4, FP16>(a_data, b_data);
                case BF16: return vector_dot_product::compute_vector_dot_product<INT4, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<INT4, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<INT4, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case INT8:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<INT8, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<INT8, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<INT8, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<INT8, FP16>(a_data, b_data);
                case BF16: return vector_dot_product::compute_vector_dot_product<INT8, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<INT8, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<INT8, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case INT16:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<INT16, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<INT16, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<INT16, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<INT16, FP16>(a_data, b_data);
                case BF16: return vector_dot_product::compute_vector_dot_product<INT16, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<INT16, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<INT16, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case FP16:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<FP16, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<FP16, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<FP16, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<FP16, FP16>(a_data, b_data);
                case BF16: return vector_dot_product::compute_vector_dot_product<FP16, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<FP16, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<FP16, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case BF16:
        case BBF16:  // BBF16 uses same processing as BF16
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<BF16, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<BF16, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<BF16, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<BF16, FP16>(a_data, b_data);
                case BF16:
                case BBF16: return vector_dot_product::compute_vector_dot_product<BF16, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<BF16, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<BF16, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case FP8E4:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<FP8E4, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<FP8E4, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<FP8E4, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<FP8E4, FP16>(a_data, b_data);
                case BF16:
                case BBF16: return vector_dot_product::compute_vector_dot_product<FP8E4, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<FP8E4, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<FP8E4, FP8E5>(a_data, b_data);
                default: return 0;
            }
        case FP8E5:
            switch (b_data_type) {
                case INT4: return vector_dot_product::compute_vector_dot_product<FP8E5, INT4>(a_data, b_data);
                case INT8: return vector_dot_product::compute_vector_dot_product<FP8E5, INT8>(a_data, b_data);
                case INT16: return vector_dot_product::compute_vector_dot_product<FP8E5, INT16>(a_data, b_data);
                case FP16: return vector_dot_product::compute_vector_dot_product<FP8E5, FP16>(a_data, b_data);
                case BF16:
                case BBF16: return vector_dot_product::compute_vector_dot_product<FP8E5, BF16>(a_data, b_data);
                case FP8E4: return vector_dot_product::compute_vector_dot_product<FP8E5, FP8E4>(a_data, b_data);
                case FP8E5: return vector_dot_product::compute_vector_dot_product<FP8E5, FP8E5>(a_data, b_data);
                default: return 0;
            }
        default:
            return 0;  // Unsupported data type
    }
}

/**
 * @brief C wrapper with automatic pre-alignment
 */
uint32_t vector_dot_product_auto_c(const uint16_t* a_raw, uint8_t a_data_type,
                                   const uint16_t* b_raw, uint8_t b_data_type) {
    return vector_dot_product::VectorDotProductInterface::compute_with_auto_prealign(
        a_raw, a_data_type, b_raw, b_data_type);
}

} // extern "C"
